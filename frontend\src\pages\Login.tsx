import React, { useState } from 'react'
import { Form, Input, Button, Card, Typography, message, Checkbox } from 'antd'
import { UserOutlined, LockOutlined, RobotOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Text } = Typography

interface LoginFormData {
  username: string
  password: string
  remember: boolean
}

// Dummy user data for authentication
const DUMMY_USERS = [
  {
    username: 'admin',
    password: 'admin123',
    role: 'Administrator',
    name: 'Admin User'
  },
  {
    username: 'manager',
    password: 'manager123',
    role: 'Manager',
    name: 'Manager User'
  },
  {
    username: 'user',
    password: 'user123',
    role: 'User',
    name: 'Regular User'
  }
]

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  const [form] = Form.useForm()

  const handleLogin = async (values: LoginFormData) => {
    setLoading(true)
    
    // Simulate API call delay
    setTimeout(() => {
      const user = DUMMY_USERS.find(
        u => u.username === values.username && u.password === values.password
      )

      if (user) {
        // Store user info in localStorage (in real app, use proper auth state management)
        localStorage.setItem('user', JSON.stringify({
          username: user.username,
          name: user.name,
          role: user.role,
          isAuthenticated: true
        }))
        
        message.success(`Welcome back, ${user.name}!`)
        navigate('/dashboard')
      } else {
        message.error('Invalid username or password')
      }
      
      setLoading(false)
    }, 1000)
  }

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      margin: 0,
      position: 'fixed',
      top: 0,
      left: 0,
      overflow: 'auto'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
          borderRadius: '12px'
        }}
        styles={{ body: { padding: '40px' } }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <RobotOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            Chatbot Admin
          </Title>
          <Text type="secondary">Sign in to your account</Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: 'Please input your username!' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: 'Please input your password!' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Password"
            />
          </Form.Item>

          <Form.Item name="remember" valuePropName="checked">
            <Checkbox>Remember me</Checkbox>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%', height: '44px' }}
            >
              Sign In
            </Button>
          </Form.Item>
        </Form>

        <div style={{ marginTop: '24px', padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}>
          <Text strong style={{ display: 'block', marginBottom: '8px' }}>Demo Accounts:</Text>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <div>👤 <strong>admin</strong> / admin123 (Administrator)</div>
            <div>👤 <strong>manager</strong> / manager123 (Manager)</div>
            <div>👤 <strong>user</strong> / user123 (User)</div>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default Login
